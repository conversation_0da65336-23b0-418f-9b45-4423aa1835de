USE [GNSNCS]
GO

/****** Object:  StoredProcedure [AppData].[gnsInventory_join_ups_site_list_select_dynamic] ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- CORRECTED VERSION of the stored procedure
-- Fixed issues: typos, missing parameters, syntax errors

ALTER PROCEDURE [AppData].[gnsInventory_join_ups_site_list_select_dynamic]
    @devType int = null,
    @className varchar(32) = null,
    @devName varchar(255) = null,
    @devIPAddr varchar(15) = null,
    @devHostName varchar(255) = null,
    @devMACAddr varchar(50) = null,
    @devModelName varchar(400) = null,
    @bldMnemonic varchar(25) = null,
    @reg_code varchar(25) = null,
    @dist_code varchar(25) = null,
    @debug bit = 0 
AS  
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @SQLQuery AS NVARCHAR(MAX);
    DECLARE @ParamDefinition AS NVARCHAR(MAX);
    
    SET @ParamDefinition = N'@devType int, 
        @className varchar(32), 
        @devName varchar(255), 
        @devIPAddr varchar(15), 
        @devHostName varchar(255), 
        @devMACAddr varchar(50), 
        @devModelName varchar(400), 
        @bldMnemonic varchar(25), 
        @reg_code varchar(25), 
        @dist_code varchar(25)';

    -- Fixed the INNER JOIN typo (was "INNER OIN")
    SET @SQLQuery = 'SELECT DISTINCT 
        i.devName,
        i.devIPAddr, 
        i.bldMnemonic, 
        s.reg_num, 
        s.site_name, 
        s.dist_num,
        i.className,
        i.devHostName,
        i.devMACAddr,
        i.devModelName,
        i.devType
    FROM [GNSNCS].[AppData].[gnsInventory] i
    INNER JOIN [AppData].[ups_site_list] s 
        ON s.bldMnemonic = i.bldMnemonic
    WHERE 1=1 ';

    -- Fixed variable name consistency (@dev_name vs @devName)
    IF @devName IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.devName LIKE @devName + ''%''';
        
    IF @className IS NOT NULL   
        SET @SQLQuery = @SQLQuery + ' AND i.className LIKE @className + ''%''';
        
    IF @devIPAddr IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.devIPAddr LIKE @devIPAddr + ''%''';
        
    IF @devHostName IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.devHostName LIKE @devHostName + ''%''';
        
    IF @devMACAddr IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.devMACAddr LIKE @devMACAddr + ''%''';
        
    IF @devModelName IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.devModelName LIKE @devModelName + ''%''';
        
    IF @bldMnemonic IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.bldMnemonic LIKE @bldMnemonic + ''%''';
        
    IF @devType IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND i.devType = @devType';
        
    -- Fixed column name (reg_nu -> reg_num)
    IF @reg_code IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND s.reg_num LIKE @reg_code + ''%''';
        
    -- Fixed column name (dist_num should match parameter)
    IF @dist_code IS NOT NULL
        SET @SQLQuery = @SQLQuery + ' AND s.dist_num LIKE @dist_code + ''%''';

    -- Add ORDER BY for consistent results
    SET @SQLQuery = @SQLQuery + ' ORDER BY i.devName, i.devIPAddr';

    IF @debug = 1
        PRINT @SQLQuery;
    ELSE
        EXECUTE sp_executesql @SQLQuery, @ParamDefinition, 
            @devType, @className, @devName, @devIPAddr, @devHostName, 
            @devMACAddr, @devModelName, @bldMnemonic, @reg_code, @dist_code;
END
GO

-- Grant execute permissions (adjust as needed for your security model)
-- GRANT EXECUTE ON [AppData].[gnsInventory_join_ups_site_list_select_dynamic] TO [your_web_user];
