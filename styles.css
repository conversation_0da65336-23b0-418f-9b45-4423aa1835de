/* Network Inventory Search Application Styles */

body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

.search-form {
    background-color: #f8f9fa;
    padding: 25px;
    border-radius: 6px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

input, select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

input:focus, select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.search-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: background-color 0.3s;
}

.btn-search {
    background-color: #007bff;
    color: white;
}

.btn-search:hover {
    background-color: #0056b3;
}

.btn-clear {
    background-color: #6c757d;
    color: white;
}

.btn-clear:hover {
    background-color: #545b62;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.results-container {
    margin-top: 30px;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: white;
}

.results-table th,
.results-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.results-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #495057;
    position: sticky;
    top: 0;
}

.results-table tr:hover {
    background-color: #f5f5f5;
}

.no-results {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
    border: 1px solid #f5c6cb;
}

.results-count {
    margin-bottom: 15px;
    font-weight: bold;
    color: #495057;
}

/* Responsive design improvements */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .search-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    button {
        width: 100%;
        max-width: 200px;
    }
    
    .results-table {
        font-size: 14px;
    }
    
    .results-table th,
    .results-table td {
        padding: 8px;
    }
}

/* Print styles */
@media print {
    body {
        background-color: white;
        padding: 0;
    }
    
    .container {
        box-shadow: none;
        padding: 0;
    }
    
    .search-form,
    .search-buttons,
    .loading {
        display: none;
    }
    
    .results-table {
        border: 1px solid #000;
    }
    
    .results-table th,
    .results-table td {
        border: 1px solid #000;
    }
}
