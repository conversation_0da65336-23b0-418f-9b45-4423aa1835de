#!/usr/bin/perl

# Database Configuration Template
# Copy this file and update with your actual database connection details

package Config;

use strict;
use warnings;

# Database connection settings
our $DB_SERVER = 'your_sql_server_name_or_ip';
our $DB_NAME = 'GNSNCS';
our $DB_USERNAME = 'your_database_username';
our $DB_PASSWORD = 'your_database_password';

# ODBC Driver settings
our $ODBC_DRIVER = 'ODBC Driver 17 for SQL Server';

# Alternative drivers you might use:
# our $ODBC_DRIVER = 'SQL Server';
# our $ODBC_DRIVER = 'SQL Server Native Client 11.0';

# Stored procedure name (should match your database)
our $STORED_PROC = '[AppData].[gnsInventory_join_ups_site_list_select_dynamic]';

# Debug mode (set to 1 to enable debug output)
our $DEBUG = 0;

1; # Return true for module loading
