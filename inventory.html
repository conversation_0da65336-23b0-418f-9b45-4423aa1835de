<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Search</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .search-form {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 6px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }

        input, select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .search-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-search {
            background-color: #007bff;
            color: white;
        }

        .btn-search:hover {
            background-color: #0056b3;
        }

        .btn-clear {
            background-color: #6c757d;
            color: white;
        }

        .btn-clear:hover {
            background-color: #545b62;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .results-container {
            margin-top: 30px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: white;
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .results-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            position: sticky;
            top: 0;
        }

        .results-table tr:hover {
            background-color: #f5f5f5;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .results-count {
            margin-bottom: 15px;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Network Inventory Search</h1>

        <form class="search-form" hx-post="search.pl" hx-target="#results" hx-indicator="#loading">
            <div class="form-grid">
                <div class="form-group">
                    <label for="devType">Device Type:</label>
                    <input type="number" id="devType" name="devType" placeholder="Enter device type number">
                </div>

                <div class="form-group">
                    <label for="className">Class Name:</label>
                    <input type="text" id="className" name="className" placeholder="Enter class name" maxlength="32">
                </div>

                <div class="form-group">
                    <label for="devName">Device Name:</label>
                    <input type="text" id="devName" name="devName" placeholder="Enter device name" maxlength="255">
                </div>

                <div class="form-group">
                    <label for="devIPAddr">IP Address:</label>
                    <input type="text" id="devIPAddr" name="devIPAddr" placeholder="Enter IP address" maxlength="15">
                </div>

                <div class="form-group">
                    <label for="devHostName">Host Name:</label>
                    <input type="text" id="devHostName" name="devHostName" placeholder="Enter host name" maxlength="255">
                </div>

                <div class="form-group">
                    <label for="devMACAddr">MAC Address:</label>
                    <input type="text" id="devMACAddr" name="devMACAddr" placeholder="Enter MAC address" maxlength="50">
                </div>

                <div class="form-group">
                    <label for="devModelName">Model Name:</label>
                    <input type="text" id="devModelName" name="devModelName" placeholder="Enter model name" maxlength="400">
                </div>

                <div class="form-group">
                    <label for="bldMnemonic">Building Mnemonic:</label>
                    <input type="text" id="bldMnemonic" name="bldMnemonic" placeholder="Enter building mnemonic" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="reg_code">Region Code:</label>
                    <input type="text" id="reg_code" name="reg_code" placeholder="Enter region code" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="dist_code">District Code:</label>
                    <input type="text" id="dist_code" name="dist_code" placeholder="Enter district code" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="bldUTCOffset">UTC Offset:</label>
                    <input type="text" id="bldUTCOffset" name="bldUTCOffset" placeholder="Enter UTC offset" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="devCodeLevel">Code Level:</label>
                    <input type="text" id="devCodeLevel" name="devCodeLevel" placeholder="Enter code level" maxlength="100">
                </div>

                <div class="form-group">
                    <label for="sysobjectid">System Object ID:</label>
                    <input type="text" id="sysobjectid" name="sysobjectid" placeholder="Enter system object ID" maxlength="128">
                </div>
            </div>

            <div class="search-buttons">
                <button type="submit" class="btn-search">Search Inventory</button>
                <button type="button" class="btn-clear" onclick="clearForm()">Clear Form</button>
            </div>
        </form>

        <div id="loading" class="loading" style="display: none;">
            Searching inventory...
        </div>

        <div id="results" class="results-container">
            <!-- Search results will be loaded here -->
        </div>
    </div>

    <script>
        function clearForm() {
            document.querySelector('.search-form').reset();
            document.getElementById('results').innerHTML = '';
        }

        // Auto-search on form changes with debounce
        let searchTimeout;
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    // Only auto-search if at least one field has a value
                    const formData = new FormData(document.querySelector('.search-form'));
                    let hasValue = false;
                    for (let [key, value] of formData.entries()) {
                        if (value.trim() !== '') {
                            hasValue = true;
                            break;
                        }
                    }
                    if (hasValue) {
                        htmx.trigger(document.querySelector('.search-form'), 'submit');
                    }
                }, 500); // 500ms debounce
            });
        });
    </script>
</body>
</html>
