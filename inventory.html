<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Search</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Network Inventory Search</h1>

        <form class="search-form" id="searchForm" hx-post="search.pl" hx-target="#results" hx-indicator="#loading">
            <div class="form-grid">
                <div class="form-group">
                    <label for="devType">Device Type:</label>
                    <input type="number" id="devType" name="devType" placeholder="Enter device type number">
                </div>

                <div class="form-group">
                    <label for="className">Class Name:</label>
                    <input type="text" id="className" name="className" placeholder="Enter class name" maxlength="32">
                </div>

                <div class="form-group">
                    <label for="devName">Device Name:</label>
                    <input type="text" id="devName" name="devName" placeholder="Enter device name" maxlength="255">
                </div>

                <div class="form-group">
                    <label for="devIPAddr">IP Address:</label>
                    <input type="text" id="devIPAddr" name="devIPAddr" placeholder="Enter IP address" maxlength="15">
                </div>

                <div class="form-group">
                    <label for="devHostName">Host Name:</label>
                    <input type="text" id="devHostName" name="devHostName" placeholder="Enter host name" maxlength="255">
                </div>

                <div class="form-group">
                    <label for="devMACAddr">MAC Address:</label>
                    <input type="text" id="devMACAddr" name="devMACAddr" placeholder="Enter MAC address" maxlength="50">
                </div>

                <div class="form-group">
                    <label for="devModelName">Model Name:</label>
                    <input type="text" id="devModelName" name="devModelName" placeholder="Enter model name" maxlength="400">
                </div>

                <div class="form-group">
                    <label for="bldMnemonic">Building Mnemonic:</label>
                    <input type="text" id="bldMnemonic" name="bldMnemonic" placeholder="Enter building mnemonic" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="reg_code">Region Code:</label>
                    <input type="text" id="reg_code" name="reg_code" placeholder="Enter region code" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="dist_code">District Code:</label>
                    <input type="text" id="dist_code" name="dist_code" placeholder="Enter district code" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="bldUTCOffset">UTC Offset:</label>
                    <input type="text" id="bldUTCOffset" name="bldUTCOffset" placeholder="Enter UTC offset" maxlength="25">
                </div>

                <div class="form-group">
                    <label for="devCodeLevel">Code Level:</label>
                    <input type="text" id="devCodeLevel" name="devCodeLevel" placeholder="Enter code level" maxlength="100">
                </div>

                <div class="form-group">
                    <label for="sysobjectid">System Object ID:</label>
                    <input type="text" id="sysobjectid" name="sysobjectid" placeholder="Enter system object ID" maxlength="128">
                </div>
            </div>

            <div class="search-buttons">
                <button type="submit" class="btn-search" onclick="hideSearchFormAfterSubmit()">Search Inventory</button>
                <button type="button" class="btn-clear" onclick="clearForm()">Clear Form</button>
            </div>
        </form>

        <!-- New Search Button (initially hidden) -->
        <div id="newSearchContainer" class="new-search-container" style="display: none;">
            <button type="button" class="btn-new-search" onclick="showSearchForm()">
                <span>🔍</span> New Search
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            Searching inventory...
        </div>

        <div id="results" class="results-container">
            <!-- Search results will be loaded here -->
        </div>
    </div>

    <script>
        function clearForm() {
            document.querySelector('.search-form').reset();
            document.getElementById('results').innerHTML = '';
            showSearchForm(); // Show search form when clearing
        }

        function hideSearchFormAfterSubmit() {
            // Use a small delay to allow the form submission to process
            setTimeout(() => {
                hideSearchForm();
            }, 100);
        }

        function hideSearchForm() {
            document.getElementById('searchForm').style.display = 'none';
            document.getElementById('newSearchContainer').style.display = 'block';
        }

        function showSearchForm() {
            document.getElementById('searchForm').style.display = 'block';
            document.getElementById('newSearchContainer').style.display = 'none';
            document.getElementById('results').innerHTML = ''; // Clear previous results
        }

        // Auto-search functionality disabled - searches only happen on button click

        // Listen for HTMX events to handle form visibility
        document.body.addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.target.id === 'results') {
                // Check if we got results
                const resultsContent = event.detail.target.innerHTML;
                if (resultsContent.trim() !== '') {
                    hideSearchForm();
                }
            }
        });

        // Handle HTMX errors - show search form again
        document.body.addEventListener('htmx:responseError', function(event) {
            showSearchForm();
        });

        // Table sorting functionality
        function makeSortable() {
            const table = document.querySelector('.results-table');
            if (!table) return;

            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => sortTable(index));
            });
        }

        function sortTable(columnIndex) {
            const table = document.querySelector('.results-table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Get current sort direction
            const header = table.querySelectorAll('th')[columnIndex];
            const currentSort = header.getAttribute('data-sort') || 'none';
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';

            // Clear all sort indicators
            table.querySelectorAll('th').forEach(th => {
                th.removeAttribute('data-sort');
                th.classList.remove('sort-asc', 'sort-desc');
            });

            // Set new sort indicator
            header.setAttribute('data-sort', newSort);
            header.classList.add(newSort === 'asc' ? 'sort-asc' : 'sort-desc');

            // Sort rows
            rows.sort((a, b) => {
                const aText = a.cells[columnIndex].textContent.trim();
                const bText = b.cells[columnIndex].textContent.trim();

                // Try to parse as numbers first
                const aNum = parseFloat(aText);
                const bNum = parseFloat(bText);

                let comparison = 0;
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    // Numeric comparison
                    comparison = aNum - bNum;
                } else {
                    // String comparison (case insensitive)
                    comparison = aText.toLowerCase().localeCompare(bText.toLowerCase());
                }

                return newSort === 'asc' ? comparison : -comparison;
            });

            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }

        // Make table sortable after HTMX loads results
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'results') {
                makeSortable();
            }
        });
    </script>
</body>
</html>
