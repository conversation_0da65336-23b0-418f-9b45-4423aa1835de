#!/usr/bin/perl

use strict;
use warnings;

print "Content-Type: text/html\n\n";
print "<html><head><title>Setup Test</title></head><body>";
print "<h1>Perl CGI Setup Test</h1>";

# Test basic Perl functionality
print "<h2>Basic Perl Test</h2>";
print "<p>✓ Perl is working correctly</p>";
print "<p>Perl version: $]</p>";

# Test required modules
print "<h2>Required Modules Test</h2>";

my @required_modules = qw(CGI DBI HTML::Entities JSON);
my @optional_modules = qw(DBD::ODBC);

foreach my $module (@required_modules) {
    eval "require $module";
    if ($@) {
        print "<p>❌ $module - NOT FOUND</p>";
        print "<p style='color: red; margin-left: 20px;'>Error: $@</p>";
    } else {
        print "<p>✓ $module - OK</p>";
    }
}

print "<h3>Optional Modules (for SQL Server)</h3>";
foreach my $module (@optional_modules) {
    eval "require $module";
    if ($@) {
        print "<p>⚠️ $module - NOT FOUND (needed for SQL Server)</p>";
    } else {
        print "<p>✓ $module - OK</p>";
    }
}

# Test configuration file
print "<h2>Configuration Test</h2>";
eval {
    require './config.pl';
    print "<p>✓ config.pl loaded successfully</p>";
    print "<p>Database Server: $Config::DB_SERVER</p>";
    print "<p>Database Name: $Config::DB_NAME</p>";
    print "<p>ODBC Driver: $Config::ODBC_DRIVER</p>";
} or do {
    print "<p>⚠️ config.pl not found or has errors</p>";
    print "<p style='color: orange; margin-left: 20px;'>This is normal if you haven't configured it yet</p>";
};

# Test CGI functionality
print "<h2>CGI Environment Test</h2>";
print "<p>Server Software: " . ($ENV{SERVER_SOFTWARE} || 'Unknown') . "</p>";
print "<p>Request Method: " . ($ENV{REQUEST_METHOD} || 'Unknown') . "</p>";
print "<p>Script Name: " . ($ENV{SCRIPT_NAME} || 'Unknown') . "</p>";

print "<h2>Next Steps</h2>";
print "<ol>";
print "<li>If any required modules are missing, install them using: <code>cpan install ModuleName</code></li>";
print "<li>Configure your database connection in config.pl</li>";
print "<li>Test the search functionality with inventory.html</li>";
print "</ol>";

print "</body></html>";
