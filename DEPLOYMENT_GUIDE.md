# Deployment Guide - Network Inventory Search Application

## Quick Start Checklist

### 1. File Deployment
Copy these files to your web server:

**Web Root Directory** (e.g., `/var/www/html/` or `C:\inetpub\wwwroot\`):
- ✅ `inventory.html` - Main application page

**CGI Directory** (e.g., `/var/www/cgi-bin/` or `C:\inetpub\wwwroot\cgi-bin\`):
- ✅ `search.pl` - Main search script
- ✅ `config.pl` - Configuration template
- ✅ `test_setup.pl` - Setup verification script
- ✅ `.htaccess` - Apache configuration (if using Apache)

### 2. Database Setup
- ✅ Run `gnsInventory.sql` to create the table structure
- ✅ Run `gnsInventory_join_ups_site_list_select_dynamic_CORRECTED.sql` to create/fix the stored procedure
- ✅ Grant appropriate permissions to your web application database user

### 3. Configuration
- ✅ Copy `config.pl` and update with your database credentials
- ✅ Set file permissions: `chmod 755 *.pl`
- ✅ Test configuration with `test_setup.pl`

### 4. Web Server Configuration
- ✅ Enable CGI execution for .pl files
- ✅ Ensure Perl modules are installed
- ✅ Configure appropriate security settings

## File Descriptions

### Core Application Files

| File | Purpose | Location |
|------|---------|----------|
| `inventory.html` | Main search interface with HTMX | Web root |
| `search.pl` | Perl CGI script for database queries | CGI directory |
| `config.pl` | Database configuration template | CGI directory |

### Database Files

| File | Purpose | Notes |
|------|---------|-------|
| `gnsInventory.sql` | Table structure | Original table definition |
| `gnsInventory_join_ups_site_list_select_dynamic` | Original stored procedure | Has syntax errors |
| `gnsInventory_join_ups_site_list_select_dynamic_CORRECTED.sql` | Fixed stored procedure | Use this version |

### Support Files

| File | Purpose | Notes |
|------|---------|-------|
| `test_setup.pl` | Setup verification script | Test Perl/modules/config |
| `.htaccess` | Apache configuration | For Apache servers only |
| `README.md` | Comprehensive documentation | Full setup instructions |
| `DEPLOYMENT_GUIDE.md` | This file | Quick deployment guide |

## Quick Configuration Steps

### 1. Database Configuration
Edit `config.pl`:
```perl
our $DB_SERVER = 'your_sql_server_name';
our $DB_NAME = 'GNSNCS';
our $DB_USERNAME = 'your_db_username';
our $DB_PASSWORD = 'your_db_password';
```

### 2. Test Setup
1. Navigate to: `http://yourserver/cgi-bin/test_setup.pl`
2. Verify all modules are installed
3. Check database configuration

### 3. Launch Application
1. Navigate to: `http://yourserver/inventory.html`
2. Enter search criteria
3. Verify results are returned

## Common Issues & Solutions

### "Permission Denied"
```bash
chmod 755 /path/to/cgi-bin/*.pl
chown www-data:www-data /path/to/cgi-bin/*.pl  # Linux
```

### "Module Not Found"
```bash
# Install missing Perl modules
cpan install CGI DBI HTML::Entities JSON DBD::ODBC
```

### "Database Connection Failed"
- Verify SQL Server is running and accessible
- Check firewall settings
- Verify ODBC driver is installed
- Test connection with SQL Server Management Studio

### "Stored Procedure Not Found"
- Use the CORRECTED version of the stored procedure
- Verify the procedure exists: `SELECT * FROM sys.procedures WHERE name LIKE '%gnsInventory%'`
- Check user permissions: `GRANT EXECUTE ON [procedure_name] TO [user]`

## Security Checklist

- ✅ Database user has minimal required permissions
- ✅ Config file is not web-accessible (protected by .htaccess)
- ✅ SQL files are not web-accessible
- ✅ Input validation is enabled in search.pl
- ✅ HTML encoding prevents XSS attacks
- ✅ Parameterized queries prevent SQL injection

## Performance Optimization

### Database
- ✅ Add indexes on frequently searched columns
- ✅ Consider adding TOP clause to limit large result sets
- ✅ Monitor query performance

### Web Server
- ✅ Enable compression for HTML/CSS/JS
- ✅ Configure appropriate caching headers
- ✅ Monitor CGI process limits

## Monitoring & Maintenance

### Log Files to Monitor
- Web server error logs
- Database query logs
- Application-specific logs (if enabled)

### Regular Maintenance
- Monitor disk space usage
- Review and rotate log files
- Update Perl modules as needed
- Review database performance

## Support Information

### Required Perl Modules
- CGI (core module)
- DBI
- HTML::Entities
- JSON
- DBD::ODBC (for SQL Server)

### Tested Environments
- **Web Servers**: Apache 2.4+, IIS 8+
- **Perl**: 5.16+
- **Databases**: SQL Server 2012+
- **Browsers**: Chrome, Firefox, Safari, Edge (modern versions)

### Getting Help
1. Check web server error logs
2. Run `test_setup.pl` to verify configuration
3. Enable debug mode in `config.pl`
4. Review the comprehensive `README.md` file

## Next Steps After Deployment

1. **Test thoroughly** with various search criteria
2. **Train users** on the search interface
3. **Monitor performance** and optimize as needed
4. **Plan for backups** of configuration and customizations
5. **Document any customizations** for future maintenance
