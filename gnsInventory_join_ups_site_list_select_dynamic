USE [GNSNCS]
GO
/****** Object:  StoredProcedure [dbo].[gnsInventory_join_ups_site_list_select_dynamic]    Script Date: 01/11/2019 10:10:00 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [  AppData].[gnsInventory_join_ups_site_list_select_dynamic
    @devType int = null
    ,@className varchar(32) = null
    ,@devName varchar(255) = null
    ,@devIPAddr varchar(15) = null
    ,@devHostName varchar(255) = null
    ,@devMACAddr varchar(50) = null
    ,@devModelName varchar(400) = null
    ,@devSerialNum varchar(100) = null
    ,@bldMnemonic varchar(25) = null
    ,@reg_code varchar(25) = null
    ,@dist_code varchar(25) = null
    ,@debug bit = 0 
AS  
BEGIN
SET NOCOUNT ON;
DECLARE @SQLQuery AS NVARCHAR(MAX);
DECLARE @ParamDefinition AS NVARCHAR(MAX);
set @ParamDefinition = N'@devType int, 
@className varchar(32), @devName varchar(255), @devIPAddr varchar(15), 
@devHostName varchar(255), @devMACAddr varchar(50), 
@devModelName varchar(400), @devSerialNum varchar(100), 
@bldMnemonic varchar(25), @reg_code varchar(25), 
@dist_code varchar(25)';

set @SQLQuery = 'SELECT distinct(devName),devIPAddr, bldMnemonic, [AppData].ups_site_list.reg_num, [AppData].ups_site_list.site_name, [AppData].ups_site_list.dist_num
FROM [GNSNCS].[AppData].[gnsInventory]
INNER OIN [AppData].[ups_site_list] 
ON [AppData].ups_site_list.bldMnemonic = [AppData].[gnsInventory].bldMnemonic
WHERE 1=1 ';

IF @dev_name is not null
    set @SQLQuery = @SQLQuery + ' AND devName LIKE ''' + @devName + '%'')';
if @className is not null   
    set @SQLQuery = @SQLQuery + ' AND className LIKE ''' +  @className + '%'')';
if @devIPAddr is not NULL
    set @SQLQuery = @SQLQuery + ' AND devIPAddr LIKE ''' + @devIPAddr + '%'')';
if @devHostName is not NULL
    set @SQLQuery = @SQLQuery + ' AND devHostName LIKE ''' + @devHostName + '%'')';
if @devMACAddr is not NULL
    set @SQLQuery = @SQLQuery + ' AND devMACAddr LIKE ''' + @devMACAddr + '%'')';
if @devModelName is not NULL
    set @SQLQuery = @SQLQuery + ' AND devModelName LIKE ''' + @devModelName + '%'')';
if @devSerialNum is not NULL
    set @SQLQuery = @SQLQuery + ' AND devSerialNum LIKE ''' + @devSerialNum + '%'')';
if @bldMnemonic is not NULL
    set @SQLQuery = @SQLQuery + ' AND bldMnemonic LIKE ''' + @bldMnemonic + '%'')';     
if @devType is not NULL
    set @SQLQuery = @SQLQuery + ' AND devType = ' + cast(@devType as varchar(10)) + ')';
if @reg_num is not NULL
    set @SQLQuery = @SQLQuery + ' AND reg_nu = ''' + @reg_num + ''')';
if @dist_num is not NULL
    set @SQLQuery = @SQLQuery + ' AND dist_num = ''' + @dist_num + ''')';


if @debug = 1
    print @SQLQuery;
else
        EXECUTE sp_executesql @SQLQuery, @ParamDefinition, @devType, 
        @className, @devName, @devIPAddr, @devHostName, @devMACAddr, 
        @devModelName, @bldMnemonic, @reg_num, @dist_num;
END 