# Network Inventory Search Application

A single-page web application for searching network inventory data using HTMX and Perl backend.

## Features

- **Dynamic Search**: Search across multiple database fields with real-time results
- **HTMX Integration**: Smooth, AJAX-like interactions without page refreshes
- **Responsive Design**: Works on desktop and mobile devices
- **Auto-search**: Automatically searches as you type (with debounce)
- **Secure Database Access**: Uses stored procedures for database interactions

## Files

- `inventory.html` - Main HTML page with search form and HTMX integration
- `styles.css` - CSS stylesheet for the application
- `search.pl` - Perl CGI script that handles database queries
- `config.pl` - Configuration template for database connection
- `gnsInventory.sql` - Database table structure
- `gnsInventory_join_ups_site_list_select_dynamic` - Stored procedure definition

## Searchable Fields

The application allows searching on the following fields (excludes netCoolID and devSerialNum as requested):

- **Device Type** - Numeric device type identifier
- **Class Name** - Device class name (max 32 chars)
- **Device Name** - Full device name (max 255 chars)
- **IP Address** - Device IP address (max 15 chars)
- **Host Name** - Device hostname (max 255 chars)
- **MAC Address** - Device MAC address (max 50 chars)
- **Model Name** - Device model name (max 400 chars)
- **Building Mnemonic** - Building identifier (max 25 chars)
- **Region Code** - Regional code (max 25 chars)
- **District Code** - District code (max 25 chars)
- **UTC Offset** - Building UTC offset (max 25 chars)
- **Code Level** - Device code level (max 100 chars)
- **System Object ID** - SNMP system object ID (max 128 chars)

## Setup Instructions

### Prerequisites

1. **Web Server** with CGI support (Apache, IIS, etc.)
2. **Perl** with the following modules:
   - CGI
   - DBI
   - HTML::Entities
   - JSON
   - DBD::ODBC (for SQL Server connectivity)
3. **SQL Server** with the GNSNCS database
4. **ODBC Driver** for SQL Server

### Installation Steps

1. **Copy Files** to your web server directories:
   ```bash
   # Copy web files to web root
   cp inventory.html /var/www/html/
   cp styles.css /var/www/html/

   # Copy CGI scripts to CGI directory
   cp search.pl /var/www/cgi-bin/
   cp config.pl /var/www/cgi-bin/
   ```

2. **Set Permissions**:
   ```bash
   chmod 755 /var/www/cgi-bin/search.pl
   chmod 644 /var/www/cgi-bin/config.pl
   ```

3. **Configure Database Connection**:
   - Copy `config.pl` to create your configuration
   - Edit the configuration with your database details:
   ```perl
   our $DB_SERVER = 'your_sql_server';
   our $DB_NAME = 'GNSNCS';
   our $DB_USERNAME = 'your_username';
   our $DB_PASSWORD = 'your_password';
   ```

4. **Install Perl Modules** (if not already installed):
   ```bash
   cpan install CGI DBI HTML::Entities JSON DBD::ODBC
   ```

5. **Configure Web Server**:
   - Ensure CGI is enabled
   - Set appropriate directory permissions
   - Configure MIME types if needed

### Database Setup

1. **Create the Database Table** using `gnsInventory.sql`
2. **Create the Stored Procedure** using the provided SQL file
3. **Verify Permissions** - ensure the database user has EXECUTE permissions on the stored procedure

### Testing

1. **Open** `inventory.html` in a web browser
2. **Enter search criteria** in any field
3. **Click Search** or wait for auto-search to trigger
4. **Verify results** are displayed correctly

## Usage

### Basic Search
1. Enter search terms in any combination of fields
2. Click "Search Inventory" or wait for auto-search
3. Results will appear below the form

### Advanced Features
- **Partial Matching**: All text searches use LIKE with wildcard matching
- **Auto-search**: Results update automatically as you type (500ms delay)
- **Clear Form**: Reset all fields and clear results
- **Responsive Table**: Results table scrolls horizontally on small screens

## Troubleshooting

### Common Issues

1. **"Database connection failed"**
   - Check database server connectivity
   - Verify credentials in config.pl
   - Ensure ODBC driver is installed

2. **"Permission denied"**
   - Check file permissions on search.pl
   - Verify web server CGI configuration
   - Check directory permissions

3. **"Stored procedure not found"**
   - Verify stored procedure exists in database
   - Check stored procedure name in config.pl
   - Ensure user has EXECUTE permissions

4. **No results returned**
   - Check if data exists matching criteria
   - Verify stored procedure logic
   - Enable debug mode in config.pl

### Debug Mode

Enable debug mode by setting `$DEBUG = 1` in config.pl to see:
- SQL queries being executed
- Parameter values
- Database connection details

## Security Considerations

- **Input Validation**: All inputs are sanitized before database queries
- **Stored Procedures**: Uses parameterized stored procedures to prevent SQL injection
- **HTML Encoding**: All output is HTML-encoded to prevent XSS attacks
- **Database Permissions**: Use least-privilege database accounts

## Browser Compatibility

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **HTMX Support**: Requires JavaScript enabled
- **Responsive Design**: Works on mobile and desktop

## Performance Notes

- **Auto-search Debounce**: 500ms delay prevents excessive database queries
- **Efficient Queries**: Uses indexed stored procedure for optimal performance
- **Result Limiting**: Consider adding TOP clause to stored procedure for large datasets
