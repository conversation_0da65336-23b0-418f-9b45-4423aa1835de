#!/usr/bin/perl

use strict;
use warnings;
use CGI;
use DBI;
use HTML::Entities;
use JSON;
use lib '.';

# Load configuration
eval {
    require 'config.pl';
    Config->import();
};

if ($@) {
    # Fallback configuration if config.pl doesn't exist
    our $DB_SERVER = 'your_server_name';
    our $DB_NAME = 'GNSNCS';
    our $DB_USERNAME = 'your_username';
    our $DB_PASSWORD = 'your_password';
    our $ODBC_DRIVER = 'ODBC Driver 17 for SQL Server';
    our $STORED_PROC = '[AppData].[gnsInventory_join_ups_site_list_select_dynamic]';
    our $DEBUG = 0;
}

# Create CGI object
my $cgi = CGI->new;

# Print HTTP header
print $cgi->header(-type => 'text/html', -charset => 'utf-8');

# Get form parameters
my %params = (
    devType => $cgi->param('devType') || '',
    className => $cgi->param('className') || '',
    devName => $cgi->param('devName') || '',
    devIPAddr => $cgi->param('devIPAddr') || '',
    devHostName => $cgi->param('devHostName') || '',
    devMACAddr => $cgi->param('devMACAddr') || '',
    devModelName => $cgi->param('devModelName') || '',
    bldMnemonic => $cgi->param('bldMnemonic') || '',
    reg_code => $cgi->param('reg_code') || '',
    dist_code => $cgi->param('dist_code') || '',
    bldUTCOffset => $cgi->param('bldUTCOffset') || '',
    devCodeLevel => $cgi->param('devCodeLevel') || '',
    sysobjectid => $cgi->param('sysobjectid') || ''
);

# Clean parameters (remove leading/trailing whitespace)
foreach my $key (keys %params) {
    $params{$key} =~ s/^\s+|\s+$//g if $params{$key};
    $params{$key} = undef if $params{$key} eq '';
}

# Check if at least one search parameter is provided
my $has_search_criteria = 0;
foreach my $value (values %params) {
    if (defined $value && $value ne '') {
        $has_search_criteria = 1;
        last;
    }
}

if (!$has_search_criteria) {
    print_no_criteria_message();
    exit;
}

# Connect to database
my $dsn = "DBI:ODBC:Driver={$Config::ODBC_DRIVER};Server=$Config::DB_SERVER;Database=$Config::DB_NAME;";
my $dbh = DBI->connect($dsn, $Config::DB_USERNAME, $Config::DB_PASSWORD, {
    RaiseError => 1,
    PrintError => 0,
    AutoCommit => 1
});

if (!$dbh) {
    print_error("Database connection failed: " . $DBI::errstr);
    exit;
}

eval {
    # Prepare the stored procedure call
    my $sql = "{call $Config::STORED_PROC(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}";

    my $sth = $dbh->prepare($sql);
    
    # Execute with parameters
    $sth->execute(
        $params{devType},
        $params{className},
        $params{devName},
        $params{devIPAddr},
        $params{devHostName},
        $params{devMACAddr},
        $params{devModelName},
        $params{bldMnemonic},
        $params{reg_code},
        $params{dist_code},
        0  # debug parameter
    );
    
    # Fetch results
    my @results = ();
    while (my $row = $sth->fetchrow_hashref) {
        push @results, $row;
    }
    
    # Generate HTML response
    print_results(\@results, \%params);
    
    $sth->finish;
};

if ($@) {
    print_error("Database query failed: $@");
}

$dbh->disconnect if $dbh;

sub print_results {
    my ($results, $search_params) = @_;
    
    my $count = scalar @$results;
    
    print qq{<div class="results-count">Found $count record(s)</div>};
    
    if ($count == 0) {
        print qq{
            <div class="no-results">
                No records found matching your search criteria.
                <br><br>
                <strong>Search criteria used:</strong><br>
        };
        
        foreach my $key (sort keys %$search_params) {
            if (defined $search_params->{$key} && $search_params->{$key} ne '') {
                my $label = format_field_label($key);
                my $value = encode_entities($search_params->{$key});
                print qq{$label: <em>$value</em><br>};
            }
        }
        
        print qq{</div>};
        return;
    }
    
    print qq{
        <table class="results-table">
            <thead>
                <tr>
                    <th>Device Name</th>
                    <th>IP Address</th>
                    <th>Building</th>
                    <th>Region #</th>
                    <th>Site Name</th>
                    <th>District #</th>
                </tr>
            </thead>
            <tbody>
    };
    
    foreach my $row (@$results) {
        print qq{<tr>};
        print qq{<td>} . encode_entities($row->{devName} || '') . qq{</td>};
        print qq{<td>} . encode_entities($row->{devIPAddr} || '') . qq{</td>};
        print qq{<td>} . encode_entities($row->{bldMnemonic} || '') . qq{</td>};
        print qq{<td>} . encode_entities($row->{reg_num} || '') . qq{</td>};
        print qq{<td>} . encode_entities($row->{site_name} || '') . qq{</td>};
        print qq{<td>} . encode_entities($row->{dist_num} || '') . qq{</td>};
        print qq{</tr>};
    }
    
    print qq{
            </tbody>
        </table>
    };
}

sub print_error {
    my ($message) = @_;
    print qq{
        <div class="error">
            <strong>Error:</strong> } . encode_entities($message) . qq{
        </div>
    };
}

sub print_no_criteria_message {
    print qq{
        <div class="no-results">
            Please enter at least one search criterion to search the inventory.
        </div>
    };
}

sub format_field_label {
    my ($field) = @_;
    
    my %labels = (
        devType => 'Device Type',
        className => 'Class Name',
        devName => 'Device Name',
        devIPAddr => 'IP Address',
        devHostName => 'Host Name',
        devMACAddr => 'MAC Address',
        devModelName => 'Model Name',
        bldMnemonic => 'Building Mnemonic',
        reg_code => 'Region Code',
        dist_code => 'District Code',
        bldUTCOffset => 'UTC Offset',
        devCodeLevel => 'Code Level',
        sysobjectid => 'System Object ID'
    );
    
    return $labels{$field} || $field;
}
